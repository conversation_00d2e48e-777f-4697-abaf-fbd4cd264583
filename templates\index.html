<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Image Generator</title>
    <style>
        :root {
            --primary: #000000;
            --primary-hover: #333333;
            --background: #ffffff;
            --card: #f8f8f8;
            --text: #000000;
            --border: #e2e2e2;
            --muted: #666666;
        }

        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--background);
            color: var(--text);
            margin: 0;
            padding: 0;
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text);
        }

        h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .card {
            background-color: var(--card);
            border-radius: 0.5rem;
            border: 1px solid var(--border);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .flex {
            display: flex;
        }

        .gap-4 {
            gap: 1rem;
        }

        .grid {
            display: grid;
            gap: 1rem;
        }

        .grid-cols-1 {
            grid-template-columns: 1fr;
        }

        @media (min-width: 768px) {
            .md\:grid-cols-2 {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .lg\:grid-cols-4 {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
            margin-bottom: 1rem;
        }

        .tab {
            padding: 0.75rem 1rem;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }

        .tab.active {
            color: var(--primary);
            border-bottom: 2px solid var(--primary);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        input[type="text"],
        textarea,
        select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border);
            border-radius: 0.375rem;
            background-color: var(--card);
            color: var(--text);
            font-size: 1rem;
            transition: border-color 0.2s ease;
            appearance: none;
            /* Remove default appearance */
            background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px;
            padding-right: 2.5rem;
        }

        select option {
            padding: 0.75rem;
            background-color: var(--card);
            color: var(--text);
        }

        select:focus {
            outline: none;
            border-color: var(--primary);
        }

        select::-ms-expand {
            display: none;
        }

        select:hover {
            border-color: var(--primary);
        }

        input[type="file"] {
            display: none;
        }

        .file-input-label {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1rem;
            background-color: var(--background);
            border: 1px dashed var(--border);
            border-radius: 0.375rem;
            cursor: pointer;
            text-align: center;
            min-height: 100px;
        }

        .preview-image {
            max-width: 100%;
            max-height: 150px;
            /* Reduced from 200px */
            display: none;
            object-fit: contain;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
        }

        .template-item {
            border: 1px solid var(--border);
            border-radius: 0.375rem;
            padding: 0.5rem;
            cursor: pointer;
            transition: border-color 0.2s;
            text-align: center;
        }

        .template-item:hover {
            border-color: var(--primary);
        }

        .template-item img {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }

        .template-item span {
            font-size: 0.875rem;
            color: var(--muted);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            gap: 0.5rem;
            border: none;
            font-size: 1rem;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            opacity: 0.9;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid var(--border);
        }

        .btn-outline:hover {
            background-color: var(--background);
            border-color: var(--primary);
        }

        .w-full {
            width: 100%;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-end {
            justify-content: flex-end;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .mt-6 {
            margin-top: 1.5rem;
        }

        .result-grid {
            display: grid;
            gap: 1rem;
        }

        .result-item {
            position: relative;
            border-radius: 0.5rem;
            overflow: hidden;
            background-color: var(--background);
            border: 1px solid var(--border);
            height: 250px;
            /* Reduced fixed height */
            width: 100%;
        }

        .result-img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            /* Changed from cover to contain */
            padding: 0.5rem;
            /* Added padding */
        }

        .result-actions {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 0.5rem;
            display: flex;
            justify-content: space-around;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .result-item:hover .result-actions {
            opacity: 1;
        }

        .result-action {
            color: white;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.25rem;
            padding: 0.25rem;
        }

        .settings-row {
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .settings-row .form-group {
            flex: 1;
            min-width: 120px;
            margin-bottom: 0;
        }

        .loading {
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            text-align: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(99, 102, 241, 0.1);
            border-left-color: var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .trending-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .tag {
            background-color: var(--card);
            border: 1px solid var(--border);
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .tag:hover {
            border-color: var(--primary);
        }

        .tag.selected {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .history-item {
            background-color: var(--background);
            border: 1px solid var(--border);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .history-type {
            background-color: var(--primary);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .history-provider {
            background-color: var(--card);
            border: 1px solid var(--border);
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }

        .history-timestamp {
            color: var(--muted);
            font-size: 0.875rem;
        }

        .history-prompt {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .history-options {
            color: var(--muted);
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .history-images {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 0.5rem;
        }

        .history-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 0.375rem;
            border: 1px solid var(--border);
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <div class="logo">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 7v6h-6"></path>
                    <path d="M3 17v-6h6"></path>
                    <path d="M21 7l-3.3-3.3a4.8 4.8 0 0 0-6.8 0l-1.4 1.4"></path>
                    <path d="M3 17l3.3 3.3a4.8 4.8 0 0 0 6.8 0l1.4-1.4"></path>
                </svg>
                <span>NISHU store project v0.1</span>
            </div>
        </header>

        <div class="card">
            <div class="tabs">
                <div class="tab active" id="tab-trending">Generate based on trending</div>
                <div class="tab" id="tab-image">Image to Image</div>
                <div class="tab" id="tab-history">History</div>
            </div>

            <div id="image-panel" style="display: none;">
                <div class="form-group">
                    <label for="image-upload">Upload an image</label>
                    <label for="image-upload" class="file-input-label">
                        <span id="upload-text">Click or drag to upload an image</span>
                        <img id="preview-image" class="preview-image" alt="Preview">
                    </label>
                    <input type="file" id="image-upload" accept="image/*">
                </div>
                <div class="form-group">
                    <label for="image-prompt">Describe your modifications</label>
                    <textarea id="image-prompt" rows="2"
                        placeholder="Describe what you want to change about the image..."></textarea>
                </div>
            </div>

            <div id="trending-panel">
                <div class="form-group">
                    <label>Product Type</label>
                    <select id="product-type">
                        <option value="shirt">Shirt design</option>
                        <option value="mug">Mug design</option>
                        <option value="cap">Cap design</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Trending Etsy Tags</label>
                    <div class="trending-tags">
                        <div class="tag">Minimalist</div>
                        <div class="tag">Boho</div>
                        <div class="tag">Vintage</div>
                        <div class="tag">Floral</div>
                        <div class="tag">Abstract</div>
                        <div class="tag">Geometric</div>
                        <div class="tag">Typography</div>
                        <div class="tag">Animal</div>
                        <div class="tag">Nature</div>
                        <div class="tag">Holiday</div>
                        <div class="tag">Funny</div>
                        <div class="tag">Inspirational</div>
                        <div class="tag">Custom</div>
                        <div class="tag">Personalized</div>
                        <div class="tag">Gothic</div>
                        <div class="tag">Cottagecore</div>
                        <div class="tag">Rainbow</div>
                        <div class="tag">Celestial</div>
                        <div class="tag">Ocean</div>
                        <div class="tag">Mountain</div>
                        <div class="tag">Space</div>
                        <div class="tag">Dinosaur</div>
                        <div class="tag">Cat</div>
                        <div class="tag">Dog</div>
                        <div class="tag">Fantasy</div>
                        <div class="tag">Sci-Fi</div>
                        <div class="tag">Anime</div>
                        <div class="tag">Gaming</div>
                        <div class="tag">Music</div>
                        <div class="tag">Sports</div>
                    </div>
                </div>
            </div>

            <div id="history-panel" style="display: none;">
                <div class="form-group">
                    <label>Image Generation History</label>
                    <p style="color: var(--muted); font-size: 0.875rem; margin-bottom: 1rem;">
                        View all your previously generated images with their prompts and settings.
                    </p>
                </div>
                <div id="history-loading" style="display: none; text-align: center; padding: 2rem;">
                    <div class="spinner"></div>
                    <p>Loading history...</p>
                </div>
                <div id="history-content">
                    <!-- History items will be loaded here -->
                </div>
            </div>

            <div class="settings-row mt-4" id="settings-row">
                <div class="form-group">
                    <label for="image-count">Number of images</label>
                    <select id="image-count">
                        <option value="1">1 image</option>
                        <option value="2" selected>2 images</option>
                        <option value="3">3 images</option>
                        <option value="4">4 images</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="style">Style</label>
                    <select id="style">
                        <option value="photorealistic">Photorealistic</option>
                        <option value="digital-art">Digital Art</option>
                        <option value="anime">Anime</option>
                        <option value="illustration">Illustration</option>
                        <option value="3d-render">3D Render</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="model-provider">AI Provider</label>
                    <select id="model-provider">
                        <option value="google">Google Generative AI</option>
                        <option value="openai">OpenAI DALL-E</option>
                    </select>
                </div>
            </div>

            <div class="flex justify-end mt-4" id="generate-button-row">
                <button class="btn btn-primary" id="generate-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2v4"></path>
                        <path d="M12 18v4"></path>
                        <path d="m4.93 4.93 2.83 2.83"></path>
                        <path d="m16.24 16.24 2.83 2.83"></path>
                        <path d="M2 12h4"></path>
                        <path d="M18 12h4"></path>
                        <path d="m4.93 19.07 2.83-2.83"></path>
                        <path d="m16.24 7.76 2.83-2.83"></path>
                    </svg>
                    Generate Images
                </button>
            </div>
        </div>

        <div class="loading" id="loading-panel">
            <div class="spinner"></div>
            <p>Generating your images...</p>
            <p>This may take a few moments</p>
        </div>

        <div id="results-panel" style="display: none;">
            <h2>Generated Images</h2>
            <div class="result-grid" id="results-grid"></div>
        </div>
    </div>
    <script>
        // Tab switching functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // Hide all panels
                document.getElementById('image-panel').style.display = 'none';
                document.getElementById('trending-panel').style.display = 'none';
                document.getElementById('history-panel').style.display = 'none';

                // Show/hide settings and generate button based on tab
                const settingsRow = document.getElementById('settings-row');
                const generateButtonRow = document.getElementById('generate-button-row');

                // Show selected panel
                if (tab.id === 'tab-image') {
                    document.getElementById('image-panel').style.display = 'block';
                    document.getElementById('results-panel').style.display = 'none';
                    settingsRow.style.display = 'flex';
                    generateButtonRow.style.display = 'flex';
                    resultsGrid.innerHTML = '';
                } else if (tab.id === 'tab-trending') {
                    document.getElementById('trending-panel').style.display = 'block';
                    document.getElementById('results-panel').style.display = 'none';
                    settingsRow.style.display = 'flex';
                    generateButtonRow.style.display = 'flex';
                    resultsGrid.innerHTML = '';
                } else if (tab.id === 'tab-history') {
                    document.getElementById('history-panel').style.display = 'block';
                    document.getElementById('results-panel').style.display = 'none';
                    settingsRow.style.display = 'none';
                    generateButtonRow.style.display = 'none';
                    resultsGrid.innerHTML = '';
                    loadHistory();
                }
            });
        });

        // Image upload preview
        const imageUpload = document.getElementById('image-upload');
        const previewImage = document.getElementById('preview-image');
        const uploadText = document.getElementById('upload-text');

        imageUpload.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (event) => {
                    previewImage.src = event.target.result;
                    previewImage.style.display = 'block';
                    uploadText.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });

        // Generate button functionality
        const generateBtn = document.getElementById('generate-btn');
        const loadingPanel = document.getElementById('loading-panel');
        const resultsPanel = document.getElementById('results-panel');
        const resultsGrid = document.getElementById('results-grid');

        // Helper function for creating a download action
        function createDownloadAction(url, filename) {
            const a = document.createElement('a');
            a.href = url;
            a.download = filename || 'generated-image.png';
            a.className = 'result-action';
            a.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                    <polyline points="7 10 12 15 17 10"></polyline>
                    <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
            `;
            return a;
        }

        // Update grid columns based on image count
        document.getElementById('image-count').addEventListener('change', (e) => {
            const count = parseInt(e.target.value);
            resultsGrid.className = 'result-grid';

            if (count === 1) {
                resultsGrid.classList.add('grid-cols-1');
            } else if (count === 2) {
                resultsGrid.classList.add('grid-cols-1', 'md:grid-cols-2');
            } else if (count === 3) {
                resultsGrid.classList.add('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
            } else {
                resultsGrid.classList.add('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-4');
            }
        });

        // Process generation request
        generateBtn.addEventListener('click', () => {
            // Show loading spinner
            loadingPanel.style.display = 'flex';
            resultsPanel.style.display = 'none';

            // Clear previous results
            resultsGrid.innerHTML = '';

            const activeTab = document.querySelector('.tab.active').id;
            const formData = new FormData();

            if (activeTab === 'tab-image') {
                // Image to image editing
                const imageFile = document.getElementById('image-upload').files[0];
                const prompt = document.getElementById('image-prompt').value;
                // Get the selected model provider
                const modelProvider = document.getElementById('model-provider').value;

                if (!imageFile) {
                    alert('Please upload an image first');
                    loadingPanel.style.display = 'none';
                    return;
                }

                formData.append('image', imageFile);
                formData.append('prompt', prompt);

                // Choose appropriate endpoint based on model provider
                const endpoint = modelProvider === 'openai'
                    ? '/edit-openai'
                    : '/edit';

                console.log(`Using ${modelProvider} API at endpoint: ${endpoint}`);

                fetch(endpoint, {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Server responded with status ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        loadingPanel.style.display = 'none';
                        resultsPanel.style.display = 'block';

                        if (data.success) {
                            const resultItem = document.createElement('div');
                            resultItem.className = 'result-item';

                            const img = document.createElement('img');
                            img.className = 'result-img';
                            img.src = data.imageUrl;
                            img.alt = 'Edited image';
                            img.style.maxHeight = '250px'; // Control max height

                            const actions = document.createElement('div');
                            actions.className = 'result-actions';

                            const downloadBtn = createDownloadAction(data.imageUrl, 'edited-image.png');
                            actions.appendChild(downloadBtn);

                            resultItem.appendChild(img);
                            resultItem.appendChild(actions);
                            resultsGrid.appendChild(resultItem);
                        } else {
                            alert('Error: ' + (data.error || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        loadingPanel.style.display = 'none';
                        alert('Error: ' + error.message);
                        console.error(error);
                    });
            } else if (activeTab === 'tab-trending') {
                // Handle trending tab generation
                const productType = document.getElementById('product-type').value;
                const selectedTags = Array.from(document.querySelectorAll('.tag.selected')).map(tag => tag.textContent);
                const imageCount = parseInt(document.getElementById('image-count').value);
                const style = document.getElementById('style').value;

                // Get the selected model provider
                const modelProvider = document.getElementById('model-provider').value;

                formData.append('productType', productType);
                formData.append('tags', JSON.stringify(selectedTags));
                formData.append('count', imageCount);
                formData.append('style', style);

                // Choose appropriate endpoint based on model provider
                const endpoint = modelProvider === 'openai'
                    ? '/generate-trending-openai'
                    : '/generate-trending';

                console.log(`Using ${modelProvider} API at endpoint: ${endpoint}`);

                fetch(endpoint, {
                    method: 'POST',
                    body: formData
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Server responded with status ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        loadingPanel.style.display = 'none';
                        resultsPanel.style.display = 'block';

                        if (data.success && data.images && data.images.length > 0) {
                            data.images.forEach((imageUrl, i) => {
                                const resultItem = document.createElement('div');
                                resultItem.className = 'result-item';

                                const img = document.createElement('img');
                                img.className = 'result-img';
                                img.src = imageUrl;
                                img.alt = 'Generated image';
                                img.style.maxHeight = '250px'; // Control max height

                                const actions = document.createElement('div');
                                actions.className = 'result-actions';

                                const downloadBtn = createDownloadAction(imageUrl, `${productType}-${i + 1}.png`);
                                actions.appendChild(downloadBtn);

                                resultItem.appendChild(img);
                                resultItem.appendChild(actions);
                                resultsGrid.appendChild(resultItem);
                            });
                        } else {
                            alert('Error: ' + (data.error || 'No images were generated'));
                        }
                    })
                    .catch(error => {
                        loadingPanel.style.display = 'none';
                        alert('Error: ' + error.message);
                        console.error(error);
                    });
            }
        });

        // Set initial grid layout
        document.getElementById('image-count').dispatchEvent(new Event('change'));

        // Make tags selectable
        document.querySelectorAll('.trending-tags .tag').forEach(tag => {
            tag.addEventListener('click', () => {
                tag.classList.toggle('selected');
            });
        });

        // History functionality
        function loadHistory() {
            const historyLoading = document.getElementById('history-loading');
            const historyContent = document.getElementById('history-content');

            historyLoading.style.display = 'block';
            historyContent.innerHTML = '';

            fetch('/history')
                .then(response => response.json())
                .then(data => {
                    historyLoading.style.display = 'none';

                    if (data.success && data.history && data.history.length > 0) {
                        displayHistory(data.history);
                    } else {
                        historyContent.innerHTML = `
                            <div style="text-align: center; padding: 2rem; color: var(--muted);">
                                <p>No history found. Generate some images to see them here!</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    historyLoading.style.display = 'none';
                    historyContent.innerHTML = `
                        <div style="text-align: center; padding: 2rem; color: var(--muted);">
                            <p>Error loading history: ${error.message}</p>
                        </div>
                    `;
                    console.error('Error loading history:', error);
                });
        }

        function displayHistory(history) {
            const historyContent = document.getElementById('history-content');

            historyContent.innerHTML = history.map(item => {
                const timestamp = new Date(item.timestamp).toLocaleString();
                const typeLabel = item.type === 'generation' ? 'Generation' : 'Editing';
                const providerLabel = item.provider === 'google' ? 'Google AI' : 'OpenAI';

                let optionsText = '';
                if (item.type === 'generation') {
                    const tags = item.options.tags && item.options.tags.length > 0
                        ? item.options.tags.join(', ')
                        : 'None';
                    optionsText = `Product: ${item.options.product_type} | Style: ${item.options.style} | Tags: ${tags} | Count: ${item.options.count}`;
                } else {
                    optionsText = `Original image: ${item.options.original_image || 'Uploaded file'}`;
                }

                const imagesHtml = item.images.map(imageUrl =>
                    `<img src="${imageUrl}" alt="Generated image" class="history-image" onclick="window.open('${imageUrl}', '_blank')">`
                ).join('');

                return `
                    <div class="history-item">
                        <div class="history-header">
                            <div style="display: flex; gap: 0.5rem; align-items: center;">
                                <span class="history-type">${typeLabel}</span>
                                <span class="history-provider">${providerLabel}</span>
                            </div>
                            <span class="history-timestamp">${timestamp}</span>
                        </div>
                        <div class="history-prompt">${item.prompt}</div>
                        <div class="history-options">${optionsText}</div>
                        <div class="history-images">
                            ${imagesHtml}
                        </div>
                    </div>
                `;
            }).join('');
        }
    </script>
</body>

</html>