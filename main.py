from flask import Flask, render_template, request, jsonify, url_for, send_from_directory
from google import genai
from google.genai import types
from PIL import Image
from io import BytesIO
from dotenv import load_dotenv
import os
import uuid
import time
import base64
from openai import OpenAI
load_dotenv()
app = Flask(__name__)

# Create directory for saved images if it doesn't exist
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'generated_images')
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/static/generated_images/<filename>')
def serve_image(filename):
    return send_from_directory(UPLOAD_FOLDER, filename)

@app.route('/generate-trending', methods=['POST'])
def generate_trending():
    try:
        print("In the generate_trending function")
        import json
        import magic  # pip install python-magic-bin (on Windows) or python-magic on Linux/macOS
        client = genai.Client(api_key=os.getenv("GOOGLE_API"))

        # Get parameters from request
        product_type = request.form.get('productType', 'shirt')
        tags_json = request.form.get('tags', '[]')
        tags = json.loads(tags_json)
        count = int(request.form.get('count', 2))
        style = request.form.get('style', 'digital-art')

        # Build prompt based on selections
        prompt = f"Generate a {style} design for a {product_type}"
        if tags:
            prompt += f" with the following styles: {', '.join(tags)}"

        print(f"Generating with prompt: {prompt}")

        # Generate images
        response = client.models.generate_images(
            model='imagen-3.0-generate-002',
            prompt=prompt,
            config=types.GenerateImagesConfig(
                number_of_images=count,
            )
        )

        image_urls = []
        for i, generated_image in enumerate(response.generated_images):
            unique_id = str(uuid.uuid4())[:8]
            filename = f"{product_type}_{unique_id}_{i}.png"
            filepath = os.path.join(UPLOAD_FOLDER, filename)

            # Retrieve raw image bytes (updated debug logic)
            raw_data = getattr(generated_image.image, "image_bytes", None)

            if raw_data is None:
                print("No image_bytes found in response.")
                continue

            print("Raw data type:", type(raw_data))

            # If it's a string, maybe base64-encoded
            if isinstance(raw_data, str):
                try:
                    raw_data = base64.b64decode(raw_data)
                    print("Base64-decoded raw_data.")
                except Exception as decode_err:
                    print(f"Base64 decode error: {decode_err}")
                    continue

            # Optional: Check MIME type to be sure it's an image
            try:
                mime_type = magic.from_buffer(raw_data, mime=True)
                print(f"Guessed MIME type: {mime_type}")
                if not mime_type.startswith("image/"):
                    print("Returned data is not an image, skipping...")
                    continue
            except Exception as mime_err:
                print(f"MIME type check failed: {mime_err}")

            # Attempt to open image
            try:
                img = Image.open(BytesIO(raw_data))

                if img.mode != 'RGB':
                    img = img.convert('RGB')

                max_size = (800, 600)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                img.save(filepath, format='PNG')

                image_url = url_for('serve_image', filename=filename)
                image_urls.append(image_url)

            except Exception as image_err:
                print(f"Error processing image: {image_err}")

                # Save raw data as-is for debugging
                bin_path = filepath + ".bin"
                with open(bin_path, 'wb') as f:
                    f.write(raw_data)
                print(f"Saved raw image data to {bin_path}")

                continue

        if not image_urls:
            return jsonify({'success': False, 'error': 'No valid images were generated.'}), 500

        return jsonify({'success': True, 'images': image_urls})

    except Exception as e:
        print(f"Error generating images: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


# Add this new route
@app.route('/generate-trending-openai', methods=['POST'])
def generate_trending_openai():
    try:
        import json
        print("In the generate_trending_openai function")
        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        # Get parameters from request (same as generate_trending)
        product_type = request.form.get('productType', 'shirt')
        tags_json = request.form.get('tags', '[]')
        tags = json.loads(tags_json)
        count = int(request.form.get('count', 2))
        style = request.form.get('style', 'digital-art')
        
        # Build prompt based on selections
        prompt = f"Generate a {style} design for a {product_type}"
        if tags:
            prompt += f" with the following styles: {', '.join(tags)}"
            
        print(f"Generating with OpenAI, prompt: {prompt}")
        
        # Generate images with OpenAI
        response = client.images.generate(
            model="dall-e-3",
            prompt=prompt,
            n=min(count, 1),  # DALL-E 3 only supports 1 image per request
            size="1024x1024",
            quality="standard",
            response_format="b64_json"  # Get base64 encoded images
        )
        
        image_urls = []
        for i, image_data in enumerate(response.data):
            # Get base64 image data
            raw_data = base64.b64decode(image_data.b64_json)
            
            # Generate filename and path
            unique_id = str(uuid.uuid4())[:8]
            filename = f"{product_type}_{unique_id}_{i}.png"
            filepath = os.path.join(UPLOAD_FOLDER, filename)
            
            # Save image
            img = Image.open(BytesIO(raw_data))
            if img.mode != 'RGB':
                img = img.convert('RGB')
                
            max_size = (800, 600)
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            img.save(filepath, format='PNG')
            
            # Add URL to list
            image_url = url_for('serve_image', filename=filename)
            image_urls.append(image_url)
            
        if not image_urls:
            return jsonify({'success': False, 'error': 'No valid images were generated.'}), 500
            
        return jsonify({'success': True, 'images': image_urls})
    
    except Exception as e:
        print(f"Error generating images with OpenAI: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/edit', methods=['POST'])
def edit_image():
    try:
        print("In the edit_image function")
        client = genai.Client(api_key=os.getenv("GOOGLE_API"))
        
        # Get uploaded image and prompt
        if 'image' not in request.files:
            return jsonify({'success': False, 'error': 'No image uploaded'}), 400
            
        image_file = request.files['image']
        prompt = request.form.get('prompt', '')
        
        # Process image
        image = Image.open(image_file.stream)
        
        # Resize to 4:3 aspect ratio
        width, height = image.size
        target_ratio = 4/3
        current_ratio = width/height
        
        if current_ratio > target_ratio:
            # Too wide - adjust height
            new_height = int(width / target_ratio)
            image = image.resize((width, new_height))
        elif current_ratio < target_ratio:
            # Too tall - adjust width
            new_width = int(height * target_ratio)
            image = image.resize((new_width, height))
        
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp-image-generation",
            contents=[prompt, image],
            config=types.GenerateContentConfig(
                response_modalities=['Text', 'Image']
            )
        )
        
        # Handle response
        for part in response.candidates[0].content.parts:
            if part.inline_data is not None:
                edited_image = Image.open(BytesIO(part.inline_data.data))
                
                # Save with unique filename
                unique_id = str(uuid.uuid4())[:8]  # Fixed: was str.uuid.uuid4()[:8]
                filename = f"edited_{unique_id}.png"
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                
                # Resize to more reasonable dimensions
                max_size = (800, 600)
                edited_image.thumbnail(max_size, Image.Resampling.LANCZOS)
                edited_image.save(filepath)
                
                # Return URL for the frontend
                image_url = url_for('serve_image', filename=filename)
                return jsonify({'success': True, 'imageUrl': image_url})
        
        return jsonify({'success': False, 'error': 'No image was generated'}), 400
        
    except Exception as e:
        print(f"Error editing image: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
    
@app.route('/edit-openai', methods=['POST'])
def edit_image_openai():
    try:
        print("In the edit_image_openai function")
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        # Get uploaded image and prompt
        if 'image' not in request.files:
            return jsonify({'success': False, 'error': 'No image uploaded'}), 400
            
        image_file = request.files['image']
        prompt = request.form.get('prompt', '')
        
        # Process image - convert to the required format (RGBA) and save as PNG
        image = Image.open(image_file.stream)
        image = image.convert('RGBA')
        
        # Save image to a temporary file
        temp_img_path = os.path.join(UPLOAD_FOLDER, f"temp_{uuid.uuid4()}.png")
        image.save(temp_img_path)
        
        print(f"Using prompt: {prompt}")
        
        # Call OpenAI API to edit the image
        with open(temp_img_path, "rb") as img_file:
            response = client.images.edit(
                model="dall-e-2",  # DALL-E 3 doesn't support image editing yet, use DALL-E 2
                image=img_file,
                prompt=prompt,
                n=1,
                size="1024x1024",
                response_format="b64_json"
            )
        
        # Clean up temp file
        os.remove(temp_img_path)
        
        # Process the response
        if not response.data or len(response.data) == 0:
            return jsonify({'success': False, 'error': 'Failed to generate edited image'}), 500
            
        # Extract image data and save
        image_data = response.data[0]
        raw_data = base64.b64decode(image_data.b64_json)
        
        # Save with unique filename
        unique_id = str(uuid.uuid4())[:8]
        filename = f"edited_openai_{unique_id}.png"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        
        # Save the image
        edited_image = Image.open(BytesIO(raw_data))
        max_size = (800, 600)
        edited_image.thumbnail(max_size, Image.Resampling.LANCZOS)
        edited_image.save(filepath)
        
        # Return URL for the frontend
        image_url = url_for('serve_image', filename=filename)
        return jsonify({'success': True, 'imageUrl': image_url})
        
    except Exception as e:
        print(f"Error editing image with OpenAI: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
if __name__ == '__main__':
    app.run(port=6999)