#!/usr/bin/env python3

import requests
import json

def test_openai_generation():
    """Test the OpenAI image generation endpoint"""
    
    url = "http://127.0.0.1:6999/generate-trending-openai"
    
    # Test data
    data = {
        'productType': 'shirt',
        'tags': json.dumps(['Minimalist', 'Abstract']),
        'count': 1,
        'style': 'digital-art'
    }
    
    print("Testing OpenAI image generation...")
    print(f"Request data: {data}")
    
    try:
        response = requests.post(url, data=data, timeout=120)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ OpenAI generation test PASSED!")
                print(f"Generated {len(result.get('images', []))} images")
                return True
            else:
                print("❌ OpenAI generation test FAILED!")
                print(f"Error: {result.get('error')}")
                return False
        else:
            print("❌ OpenAI generation test FAILED!")
            print(f"HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI generation test FAILED with exception: {str(e)}")
        return False

def test_google_generation():
    """Test the Google image generation endpoint"""
    
    url = "http://127.0.0.1:6999/generate-trending"
    
    # Test data
    data = {
        'productType': 'shirt',
        'tags': json.dumps(['Minimalist', 'Abstract']),
        'count': 1,
        'style': 'digital-art'
    }
    
    print("\nTesting Google image generation...")
    print(f"Request data: {data}")
    
    try:
        response = requests.post(url, data=data, timeout=120)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Google generation test PASSED!")
                print(f"Generated {len(result.get('images', []))} images")
                return True
            else:
                print("❌ Google generation test FAILED!")
                print(f"Error: {result.get('error')}")
                return False
        else:
            print("❌ Google generation test FAILED!")
            print(f"HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Google generation test FAILED with exception: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing AI Image Generation Endpoints")
    print("=" * 50)
    
    # Test both endpoints
    openai_success = test_openai_generation()
    google_success = test_google_generation()
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"OpenAI Endpoint: {'✅ PASS' if openai_success else '❌ FAIL'}")
    print(f"Google Endpoint: {'✅ PASS' if google_success else '❌ FAIL'}")
    
    if openai_success and google_success:
        print("\n🎉 All tests passed! Both AI providers are working correctly.")
    elif openai_success or google_success:
        print("\n⚠️  Partial success - one provider is working.")
    else:
        print("\n💥 All tests failed - check your API keys and configuration.")
